[main] start name=user316 headless=1 config=config.yaml server=**************:10080 size=128 send=10 dur=180
=== TCP客户端启动 ===
配置文件: "config.yaml"
客户端名称: "user316"
服务器地址: "**************" : 10080
消息大小: 128 字节
测试持续时间: "180秒"
发送间隔: 10 微秒
运行模式: 无界面
[TcpShmClientWrapper] clientName= "user316" "user316"
useShm:0 serverAddress:************** serverPort:10080
last_server_name_file: /home/<USER>/tcp_qt6_moreClient3/build/ptcp/user316.lastserver
server_name_: 
Login Success
无界面模式启动，窗口将不可见
测试定时器已设置: 180 秒后自动退出
blk_sz: 21
avail_sz: 268435455
BLK_CNT: 268435455
write_idx_: 0
sent, seq_no: 1 current_time: 1755598137335340
测试时间到达，程序即将退出
客户端 "user316" 测试完成
